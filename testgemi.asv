% run_isar_processing_enhanced.m
% 主脚本，用于运行ISAR回波仿真和增强的深度融合VMD-ADMM-DCFT成像处理
clear; close all; clc;
fprintf('开始ISAR成像仿真与增强型深度融合处理...\n');
% -------------------- 1. 数据加载与仿真参数 -------------------- %
fprintf('加载/生成雷达回波数据...\n');
tic;
try
    load shipx2.mat; 
    load s_r_tm2.mat
    echo_data = s_r_tm2;
    fprintf('实际数据 shipx2_1000.mat 加载成功。\n');
    sim_params = struct();
    sim_params.Num_r = size(echo_data, 1);
    sim_params.Num_tm = size(echo_data, 2);
    sim_params.PRF = 1400; 
    sim_params.fc = 5.2e9;  
    sim_params.c = 3e8;
    sim_params.B = 80e6;    
    delta_r_res_actual = sim_params.c / (2*sim_params.B);
    r_center_actual = 0; 
    sim_params.r_axis = linspace(r_center_actual - (sim_params.Num_r/2)*delta_r_res_actual, ...
                                 r_center_actual + (sim_params.Num_r/2-1)*delta_r_res_actual, sim_params.Num_r);
    sim_params.tm = linspace(0, (sim_params.Num_tm-1)/sim_params.PRF, sim_params.Num_tm);
catch
    fprintf('未找到 shipx2_1000.mat，生成仿真数据...\n');
    [echo_data, sim_params] = generate_simulated_echo(); 
end

% --- 修改：优化预处理，只执行一次去均值 ---
% fprintf('对原始回波数据进行预处理 (去均值)...\n');
% for r_bin = 1:size(echo_data, 1)
%     echo_data(r_bin, :) = echo_data(r_bin, :) - mean(echo_data(r_bin, :));
% end
% 设置一个标志，表示已经进行过去均值处理
params_proc.preprocessing_done = true;
% --- 预处理结束 ---

fprintf('数据加载/生成完毕。耗时: %.2f 秒\n', toc);
fprintf('回波数据尺寸: %d (距离单元) x %d (方位单元)\n', size(echo_data, 1), size(echo_data, 2));

% -------------------- 2. 设置处理参数 -------------------- %
params_proc = struct();
params_proc.vmd.K = 3;                  
params_proc.vmd.alpha_vmd = 2000;       
params_proc.vmd.tau_vmd = 0;            
params_proc.vmd.tol_vmd_inner = 1e-5; % 稍提高VMD内部收敛精度
params_proc.vmd.max_iter_vmd_inner = 20; % 稍增加VMD内部迭代
params_proc.vmd.init_omega_method = 'peaks_robust'; % 'peaks' 或 'linear' 或 'peaks_robust'
params_proc.vmd.alpha_phase_guidance = 0.1; 

params_proc.phase_est.poly_order = 3; 
params_proc.phase_est.fd_search_range_factor = 0.5; 
params_proc.phase_est.ka_search_pts = 31;    
params_proc.phase_est.kb_search_pts = 31;    
params_proc.phase_est.sharpness_weight = 0.05; % 调整此权重，可能需要更小以突出匹配项
params_proc.phase_est.num_refinement_passes = 2; % 相位系数迭代优化次数

params_proc.admm_global.rho_X = 1.0;      
params_proc.admm_global.rho_U = 0.5;      
params_proc.admm_global.lambda_sparsity = 0.03; % 尝试减小稀疏权重，观察弱散射点
params_proc.admm_global.max_iter = 2;   % 可适当增加ADMM迭代
params_proc.admm_global.tol = 1e-4;     

params_proc.apply_azimuth_window = true; % 是否在最终FFT前应用窗函数

params_proc.num_azimuth = sim_params.Num_tm; 
params_proc.num_range_bins = sim_params.Num_r; 
params_proc.PRF = sim_params.PRF;
params_proc.fc = sim_params.fc;
params_proc.c = sim_params.c;
params_proc.tm_azimuth = sim_params.tm; 
params_proc.normalized_tm = (0:params_proc.num_azimuth-1) / params_proc.num_azimuth; 

% 明确设置预处理标志
params_proc.preprocessing_done = true;

% -------------------- 3. 执行增强型深度融合ISAR成像算法 -------------------- %
fprintf('开始执行增强型深度融合VMD-ADMM-DCFT ISAR成像算法...\n');
tic;
[ISAR_image_fused, dominant_mode_compensated_fft, vmd_modes_all_bins, phase_coeffs_all_bins, admm_convergence_all_bins] = ...
    perform_isar_imaging_fused_admm_enhanced(echo_data, params_proc, sim_params);
fprintf('增强型深度融合ISAR成像处理完毕。耗时: %.2f 秒\n', toc);

% -------------------- 4. 显示结果 -------------------- %
fprintf('显示成像结果...\n');
figure('Name', '原始数据和直接FFT');
subplot(1,2,1);
imagesc(sim_params.tm, sim_params.r_axis, abs(echo_data));
xlabel('慢时间 (秒)'); ylabel('距离 (米)'); title('距离压缩后的原始回波 (已预处理)'); colorbar; axis xy;

% 对直接FFT也应用预处理（如果原始数据被修改了）
echo_data_for_fft_display = echo_data; % radar_data 是 perform_isar_imaging_fused_admm_enhanced 的输入
for r_bin = 1:size(echo_data_for_fft_display, 1) % 确保显示用的直接FFT也基于去均值数据
    echo_data_for_fft_display(r_bin, :) = echo_data_for_fft_display(r_bin, :) - mean(echo_data_for_fft_display(r_bin, :));
end
raw_fft = fftshift(fft(echo_data_for_fft_display, [], 2), 2);
doppler_axis = linspace(-params_proc.PRF/2, params_proc.PRF/2, params_proc.num_azimuth);
subplot(1,2,2);
imagesc(doppler_axis, sim_params.r_axis, abs(raw_fft));
xlabel('多普勒频率 (Hz)'); ylabel('距离 (米)'); title('原始数据直接FFT (预处理后)'); colorbar; axis xy;

figure('Name', '对数尺度对比 - 直接FFT');
G_raw = 20*log10(abs(raw_fft)./max(abs(raw_fft(:)) + eps)); 
imagesc(doppler_axis, sim_params.r_axis, G_raw); caxis([-35,0]); % 调整动态范围
xlabel('多普勒频率 (Hz)'); ylabel('距离 (米)'); title('直接FFT (dB, 预处理后)'); colorbar; axis xy; colormap('jet');
ISAR_image_fused_shifted=fftshift(ISAR_image_fused,2);
figure('Name', '对数尺度对比 - 增强型融合');
G_fused = 20*log10(abs(ISAR_image_fused_shifted)./max(abs(ISAR_image_fused_shifted(:)) + eps)); 
imagesc(G_fused); caxis([-40,0]); % 调整动态范围
xlabel('多普勒频率 (Hz)'); ylabel('距离 (米)'); title('增强型融合ADMM (dB)'); colorbar; axis xy; colormap('jet');

if exist('dominant_mode_compensated_fft', 'var') && ~isempty(dominant_mode_compensated_fft)
    ISAR_image_fft_dominant_shifted = fftshift(dominant_mode_compensated_fft, 2);
    figure('Name', '对数尺度对比 - 主导模态补偿');
    G_dominant = 20*log10(abs(ISAR_image_fft_dominant_shifted)./max(abs(ISAR_image_fft_dominant_shifted(:)) + eps)); 
    imagesc(doppler_axis, sim_params.r_axis, G_dominant); caxis([-30,0]);
    xlabel('多普勒频率 (Hz)'); ylabel('距离 (米)'); title('主导模态补偿+FFT (dB)'); colorbar; axis xy; colormap('jet');
end
[~, r_idx_vis_candidates] = sort(sum(abs(echo_data).^2, 2), 'descend');
if ~isempty(r_idx_vis_candidates)
    r_idx_vis = r_idx_vis_candidates(1); 
else
    r_idx_vis = round(params_proc.num_range_bins/2); 
end
if isempty(r_idx_vis) || r_idx_vis == 0, r_idx_vis = 1; end 

% -------------------- 辅助函数区 -------------------- %
function [ISAR_image_sparse, s_compensated_dominant_mode_fft, vmd_modes_all_bins, phase_coeffs_all_bins, admm_convergence_all_bins] = ...
    perform_isar_imaging_fused_admm_enhanced(radar_data, params_proc, sim_params)

    [num_range_bins, num_azimuth] = size(radar_data);
    fprintf('  处理数据尺寸: %d x %d\n', num_range_bins, num_azimuth);

    % 确保preprocessing_done字段存在
    if ~isfield(params_proc, 'preprocessing_done')
        params_proc.preprocessing_done = false;
    end

    K_vmd = params_proc.vmd.K;
    poly_order_phase = params_proc.phase_est.poly_order;
    tm_normalized = params_proc.normalized_tm; 

    rho_X = params_proc.admm_global.rho_X;
    rho_U = params_proc.admm_global.rho_U;
    lambda_sparsity = params_proc.admm_global.lambda_sparsity;
    max_admm_iter = params_proc.admm_global.max_iter;
    admm_tol = params_proc.admm_global.tol;
    
    sharpness_weight = params_proc.phase_est.sharpness_weight; 
    num_phase_refinement_passes = params_proc.phase_est.num_refinement_passes;
    apply_azimuth_window_flag = params_proc.apply_azimuth_window;

    % ===== 新增：对整个数据进行预处理 =====
    % 1. 提取静态分量 - ISAR成像中静态分量对应零频能量
    static_component = mean(radar_data, 2);
    radar_data_dynamic = radar_data - static_component * ones(1, num_azimuth);
    
    % 2. 应用高通滤波去除接近零频的干扰 - 物理意义是去除接近静止的散射分量
    % 创建高通滤波器 (在频域)
    highpass_filter = ones(1, num_azimuth);
    dc_bandwidth = 3; % 定义零频附近的带宽（根据实际情况调整）
    highpass_filter(1:dc_bandwidth) = linspace(0.1, 0.9, dc_bandwidth); % 1是DC
    highpass_filter(num_azimuth-dc_bandwidth+1:num_azimuth) = linspace(0.9, 0.1, dc_bandwidth); % 对称
    
    % 在每个距离单元上应用高通滤波
    radar_data_filtered = zeros(size(radar_data_dynamic), 'like', radar_data);
    for r_idx = 1:num_range_bins
        % FFT到频域
        range_bin_spectrum = fft(radar_data_dynamic(r_idx, :));
        % 应用高通滤波
        range_bin_spectrum = range_bin_spectrum .* highpass_filter;
        % IFFT回时域
        radar_data_filtered(r_idx, :) = ifft(range_bin_spectrum);
    end
    
    % 使用预处理后的数据
    radar_data_processed = radar_data_filtered;
    
    % 将修改后的数据保存到处理参数中，供后续分析使用
    params_proc.static_component = static_component;
    params_proc.dynamic_component = radar_data_dynamic;
    params_proc.filtered_data = radar_data_processed;
    % ===== 预处理结束 =====

    ISAR_image_sparse = zeros(num_range_bins, num_azimuth, 'like', 1j*radar_data(1));

    s_compensated_dominant_mode_fft = zeros(num_range_bins, num_azimuth, 'like', 1j*radar_data(1));
    
    vmd_modes_all_bins = cell(num_range_bins, 1);
    phase_coeffs_all_bins = cell(num_range_bins, 1);
    admm_convergence_all_bins = cell(num_range_bins, 1);

    azimuth_window = ones(1, num_azimuth);
    if apply_azimuth_window_flag
        azimuth_window = hamming(num_azimuth)'; % 使用Hamming窗
    end

    fprintf('  开始逐距离单元处理 (共 %d 个)...\n', num_range_bins);
    % disp()

    for r_idx = 1:num_range_bins
        % 使用预处理后的数据进行处理
        signal_orig_for_range_bin = radar_data_processed(r_idx, :); % 使用高通滤波后的数据
        
        if sum(abs(signal_orig_for_range_bin).^2) < 1e-12 * num_azimuth % 调整能量阈值
            ISAR_image_sparse(r_idx, :) = fft(signal_orig_for_range_bin .* azimuth_window); 
            s_compensated_dominant_mode_fft(r_idx, :) = fft(signal_orig_for_range_bin .* azimuth_window);
            admm_iter_data_empty = struct('primal_res_X', [], 'dual_res_X', [], 'primal_res_U', [], 'dual_res_U', []);
            admm_convergence_all_bins{r_idx} = admm_iter_data_empty;
            vmd_modes_all_bins{r_idx} = zeros(K_vmd, num_azimuth, 'like', 1j*signal_orig_for_range_bin(1));
            phase_coeffs_all_bins{r_idx} = zeros(K_vmd, poly_order_phase);
            continue;
        end
        
        signal_norm_factor = max(abs(signal_orig_for_range_bin));
        if signal_norm_factor < eps, signal_norm_factor = 1; end % 避免除以零
        signal = signal_orig_for_range_bin / signal_norm_factor;
        
        % 由于已经进行了预处理，这里不需要再次去均值
        % signal = signal - mean(signal); % 再次确保VMD的输入信号是零均值的

        u_k = zeros(K_vmd, num_azimuth, 'like', 1j*signal(1));
        omega_k = zeros(K_vmd, 1);
        
        % 完全重写VMD初始化策略，基于ISAR的物理模型
        fft_signal_abs = abs(fft(signal));
        if strcmp(params_proc.vmd.init_omega_method, 'peaks_robust') || strcmp(params_proc.vmd.init_omega_method, 'peaks')
            % 在ISAR成像中，不同散射点产生不同的多普勒频率
            % 找到能量最强的频率分量，这些通常对应主要散射点
            [pks, locs] = findpeaks(fft_signal_abs, 'SortStr', 'descend', 'NPeaks', K_vmd*3); % 找更多峰，以便筛选
            
            % 排除零频和奈奎斯特频率附近的峰值
            % 在ISAR中，零频对应静态分量，这些通常不包含目标形状信息
            valid_peaks_idx = [];
            dc_avoid_range = 0.05; % 避开零频的范围(归一化频率)
            nyquist_avoid_range = 0.05; % 避开奈奎斯特频率的范围
            
            for i = 1:length(locs)
                norm_freq = (locs(i)-1)/num_azimuth;
                % 检查是否在零频或奈奎斯特频率附近
                if (norm_freq > dc_avoid_range && norm_freq < (1.0-nyquist_avoid_range))
                    valid_peaks_idx = [valid_peaks_idx, i];
                    if length(valid_peaks_idx) >= K_vmd
                        break;
                    end
                end
            end
            
            % 根据物理意义分配模态初始中心频率
            % 在ISAR中，不同中心频率对应不同旋转速度的散射点
            if ~isempty(valid_peaks_idx)
                for k = 1:min(K_vmd, length(valid_peaks_idx))
                    omega_k(k) = (locs(valid_peaks_idx(k))-1)/num_azimuth;
                end
            end
            
            % 如果有效峰值不足，根据ISAR物理模型合理分配剩余模态
            if length(valid_peaks_idx) < K_vmd
                % 在ISAR中，通常会有一些显著的多普勒频率，对应主要的运动模式
                remaining = K_vmd - length(valid_peaks_idx);
                
                % 分析频谱能量分布，找到能量集中区域
                [~, max_energy_idx] = max(fft_signal_abs(round(num_azimuth*0.1):round(num_azimuth*0.9)));
                max_energy_idx = max_energy_idx + round(num_azimuth*0.1) - 1;
                norm_max_energy_freq = (max_energy_idx-1)/num_azimuth;
                
                % 在能量集中区域周围均匀分布剩余模态
                % 这反映了ISAR中散射点可能有相似但略有不同的运动状态
                spread = 0.2; % 分布范围
                start_freq = max(0.1, norm_max_energy_freq - spread/2);
                end_freq = min(0.9, norm_max_energy_freq + spread/2);
                
                for k = length(valid_peaks_idx)+1:K_vmd
                    relative_pos = (k - length(valid_peaks_idx)) / (remaining + 1);
                    omega_k(k) = start_freq + relative_pos * (end_freq - start_freq);
                end
            end
        else % 线性分布策略
            % 在ISAR中，线性分布可以覆盖整个多普勒频谱范围
            % 但需要避开零频和奈奎斯特频率
            start_freq = 0.1; % 避开零频
            end_freq = 0.9;   % 避开奈奎斯特频率
            
            for k = 1:K_vmd
                omega_k(k) = start_freq + (k-1) * (end_freq - start_freq) / (K_vmd - 1);
            end
        end
        
        % 确保所有频率都在合理范围内 (0 < omega < 1)
        omega_k = max(0.01, min(0.99, omega_k));
        
        poly_coeffs_k = zeros(K_vmd, poly_order_phase); 
        estimated_phases_k = zeros(K_vmd, num_azimuth, 'like', 1j*signal(1)); 

        % Initialize poly_coeffs_k based on initial omega_k for fd
        if poly_order_phase >= 1
            for k_init_phase = 1:K_vmd
                % omega_k is normalized freq [0,1), convert to [-0.5, 0.5) for fd_norm
                fd_norm_init = omega_k(k_init_phase);
                if fd_norm_init > 0.5, fd_norm_init = fd_norm_init - 1; end
                poly_coeffs_k(k_init_phase, 1) = fd_norm_init;
                estimated_phases_k(k_init_phase, :) = construct_phase_poly(tm_normalized, poly_coeffs_k(k_init_phase,:));
            end
        end

        X_sparse_spectrum = fft(signal .* azimuth_window); % Initial X based on windowed signal
        
        Z_aux_X = X_sparse_spectrum; 
        Y_lagrange_X = zeros(size(X_sparse_spectrum), 'like', 1j*signal(1));
        Y_lagrange_U = zeros(size(signal), 'like', 1j*signal(1));
                                                               
        admm_iter_data = struct('primal_res_X', zeros(1,max_admm_iter), 'dual_res_X', zeros(1,max_admm_iter), ...
                                'primal_res_U', zeros(1,max_admm_iter), 'dual_res_U', zeros(1,max_admm_iter));
        u_k_prev_for_dual_U = u_k; 

        for iter_admm = 1:max_admm_iter
            X_prev_for_dual_X = X_sparse_spectrum; 
            Z_aux_X_prev_for_dual_X = Z_aux_X; 
            
            target_signal_for_vmd = signal + Y_lagrange_U / rho_U; 
            target_signal_for_vmd = target_signal_for_vmd - mean(target_signal_for_vmd); % Ensure VMD input is zero-mean

            current_phase_models_for_vmd = estimated_phases_k; % Use phases from previous ADMM iter
            [u_k, omega_k] = update_modes_admm(target_signal_for_vmd, u_k, omega_k, current_phase_models_for_vmd, params_proc, rho_U);
            
            S_reconstructed_from_modes_new = sum(u_k, 1);
            X_target_global = X_sparse_spectrum + Y_lagrange_X / rho_X;
            
            temp_sum_compensated_others_fft = zeros(1, num_azimuth, 'like', 1j*signal(1));
            for k_other_init = 1:K_vmd % Sum of (u_k_new * exp(-j*phi_k_old))
                temp_sum_compensated_others_fft = temp_sum_compensated_others_fft + ...
                    fft( (u_k(k_other_init,:) - mean(u_k(k_other_init,:))) .* exp(-1j * estimated_phases_k(k_other_init,:)) .* azimuth_window );
            end

            new_poly_coeffs_k = poly_coeffs_k; % Start with previous iteration's coeffs
            new_estimated_phases_k = estimated_phases_k;

            for k_update_phase = 1:K_vmd
                uk_current_mode_processed = u_k(k_update_phase,:) - mean(u_k(k_update_phase,:));
                current_uk_compensated_fft_old_phase = fft(uk_current_mode_processed .* exp(-1j * estimated_phases_k(k_update_phase,:)) .* azimuth_window);
                
                Residual_Target_Spectrum_k = X_target_global - (temp_sum_compensated_others_fft - current_uk_compensated_fft_old_phase);

                [coeffs_k, phase_val_k] = update_phase_coeffs_admm_enhanced(...
                    uk_current_mode_processed, ... % Pass zero-mean u_k
                    poly_coeffs_k(k_update_phase,:), ... 
                    Residual_Target_Spectrum_k, ...
                    params_proc, tm_normalized, sharpness_weight, azimuth_window);
                
                new_poly_coeffs_k(k_update_phase,:) = coeffs_k;
                new_estimated_phases_k(k_update_phase,:) = phase_val_k;
                
                temp_sum_compensated_others_fft = temp_sum_compensated_others_fft - current_uk_compensated_fft_old_phase ...
                                                + fft(uk_current_mode_processed .* exp(-1j*phase_val_k) .* azimuth_window);
            end
            poly_coeffs_k = new_poly_coeffs_k;
            estimated_phases_k = new_estimated_phases_k;
            
            % 基于ISAR物理模型的信号补偿与叠加
            % 在ISAR中，每个散射点贡献不同的相位历史，代表不同的运动轨迹
            % VMD模态可以视为具有相似运动特性的散射点群
            
            % 初始化补偿后的信号
            s_compensated_time = zeros(1, num_azimuth, 'like', signal(1));
            mode_weights = zeros(K_vmd, 1); % 模态权重
            
            % 计算模态权重 - 能量越高的模态贡献越大
            mode_energies = zeros(K_vmd, 1);
            for k_idx = 1:K_vmd
                mode_energies(k_idx) = sum(abs(u_k(k_idx,:)).^2);
            end
            total_energy = sum(mode_energies);
            if total_energy > 0
                mode_weights = mode_energies / total_energy;
            else
                mode_weights = ones(K_vmd, 1) / K_vmd;
            end
            
            % 累加各模态的贡献，每个模态表示特定运动模式的散射点
            for k_idx = 1:K_vmd
                % 模态能量阈值 - 忽略能量过低的模态
                if mode_weights(k_idx) < 0.05
                    continue;
                end
                
                % 模态相位补偿
                mode_compensated = u_k(k_idx,:) .* exp(-1j * estimated_phases_k(k_idx,:));
                
                % 模态频谱分析 - 检查频谱特性
                mode_fft = fft(mode_compensated);
                
                % 计算模态的频谱质心，表示主要多普勒频率
                freq_indices = 0:(num_azimuth-1);
                centroid_num = sum(freq_indices .* abs(mode_fft).^2);
                centroid_denom = sum(abs(mode_fft).^2);
                if centroid_denom > 0
                    freq_centroid = centroid_num / centroid_denom;
                    norm_centroid = freq_centroid / num_azimuth;
                    
                    % 检查频谱是否主要集中在零频附近
                    % 在ISAR中，零频对应静态或接近静态的散射点
                    is_dc_dominant = (norm_centroid < 0.05 || norm_centroid > 0.95);
                    
                    % 如果模态主要是静态成分，给予较低权重
                    if is_dc_dominant
                        effective_weight = mode_weights(k_idx) * 0.2; % 降低静态模态的贡献
                    else
                        effective_weight = mode_weights(k_idx);
                    end
                else
                    effective_weight = mode_weights(k_idx);
                end
                
                % 累加模态贡献
                s_compensated_time = s_compensated_time + effective_weight * mode_compensated;
            end
            
            % 应用窗函数增强频谱特性
            s_windowed = s_compensated_time .* azimuth_window;
            
            % 计算补偿后的频谱
            s_compensated_fft = fft(s_windowed);
            
            % ADMM更新
            X_sparse_spectrum = (s_compensated_fft + rho_X * (Z_aux_X - Y_lagrange_X/rho_X)) / (1 + rho_X);
            
            Z_aux_X = soft_threshold(X_sparse_spectrum + Y_lagrange_X/rho_X, lambda_sparsity/rho_X);
            
            Y_lagrange_X = Y_lagrange_X + rho_X * (X_sparse_spectrum - Z_aux_X);
            Y_lagrange_U = Y_lagrange_U + rho_U * (signal - S_reconstructed_from_modes_new); % signal is already zero-mean from VMD input
            
            norm_X_prev = norm(X_prev_for_dual_X); if norm_X_prev < eps, norm_X_prev = 1; end
            norm_Y_X_curr = norm(Y_lagrange_X); if norm_Y_X_curr < eps, norm_Y_X_curr = 1; end
            norm_signal_curr = norm(signal); if norm_signal_curr < eps, norm_signal_curr = 1; end
            norm_Y_U_curr = norm(Y_lagrange_U); if norm_Y_U_curr < eps, norm_Y_U_curr = 1; end

            primal_res_X_val = norm(X_sparse_spectrum - Z_aux_X) / norm_X_prev;
            dual_res_X_val = rho_X * norm(Z_aux_X - Z_aux_X_prev_for_dual_X) / norm_Y_X_curr;
            primal_res_U_val = norm(signal - S_reconstructed_from_modes_new) / norm_signal_curr;
            dual_res_U_val = rho_U * norm(sum(u_k,1) - sum(u_k_prev_for_dual_U,1)) / norm_Y_U_curr; 
            
            admm_iter_data.primal_res_X(iter_admm) = primal_res_X_val;
            admm_iter_data.dual_res_X(iter_admm) = dual_res_X_val;
            admm_iter_data.primal_res_U(iter_admm) = primal_res_U_val;
            admm_iter_data.dual_res_U(iter_admm) = dual_res_U_val;
            u_k_prev_for_dual_U = u_k; 

            if iter_admm > 1 && ...
               ((primal_res_X_val < admm_tol && dual_res_X_val < admm_tol) && ...
                (primal_res_U_val < admm_tol && dual_res_U_val < admm_tol))
                admm_iter_data.primal_res_X = admm_iter_data.primal_res_X(1:iter_admm);
                admm_iter_data.dual_res_X = admm_iter_data.dual_res_X(1:iter_admm);
                admm_iter_data.primal_res_U = admm_iter_data.primal_res_U(1:iter_admm);
                admm_iter_data.dual_res_U = admm_iter_data.dual_res_U(1:iter_admm);
                break;
            end
        end 
        
        ISAR_image_sparse(r_idx, :) = X_sparse_spectrum * signal_norm_factor;

        disp("Size of X_sparse_spec");
        disp(size(X_sparse_spectrum));
        disp("Size of ");
        disp(size(signal_norm_factor));

        % debug
        if (r_idx == 59)
            ISAR_image_sparse(59, 353)
        end
        vmd_modes_all_bins{r_idx} = u_k * signal_norm_factor;
        phase_coeffs_all_bins{r_idx} = poly_coeffs_k; 
        admm_convergence_all_bins{r_idx} = admm_iter_data;
        
        mode_energies = sum(abs(u_k).^2, 2);
        [~, dominant_idx_candidates] = sort(mode_energies, 'descend');
        if ~isempty(dominant_idx_candidates)
            dominant_idx = dominant_idx_candidates(1);
            dominant_phase_compensation = construct_phase_poly(tm_normalized, poly_coeffs_k(dominant_idx, :));
            
            % ISAR中，主导模态通常对应最强散射点或散射区域
            % 相位补偿反映了目标的运动参数
            s_comp_dom_mode_time = signal_orig_for_range_bin .* exp(-1j * dominant_phase_compensation);
            
            % 应用窗函数提高频谱质量
            windowed_comp = s_comp_dom_mode_time .* azimuth_window;
            
            % 计算最终的频谱
            s_compensated_dominant_mode_fft(r_idx, :) = fft(windowed_comp);
            
            % 保存主导模态及其相位信息，便于后续分析
            vmd_modes_all_bins{r_idx} = u_k * signal_norm_factor;
            phase_coeffs_all_bins{r_idx} = poly_coeffs_k;
        else
            % 对于无有效模态的情况，直接使用原始信号
            s_compensated_dominant_mode_fft(r_idx, :) = fft(signal_orig_for_range_bin .* azimuth_window);
        end
    end 
    fprintf('  所有距离单元处理完毕。\n');
end

function [poly_coeffs_updated_k, estimated_phase_updated_k] = update_phase_coeffs_admm_enhanced(...
    signal_mode_k, poly_coeffs_prev_k, Residual_Target_Spectrum_k, ...
    params_proc, tm_normalized, sharpness_weight, azimuth_window) % Added azimuth_window

    poly_order = params_proc.phase_est.poly_order;
    PRF = params_proc.PRF; 
    N = length(tm_normalized);
    num_refinement_passes = params_proc.phase_est.num_refinement_passes;

    if sum(abs(signal_mode_k)) < 1e-9 
        poly_coeffs_updated_k = zeros(1, poly_order);
        estimated_phase_updated_k = zeros(1, N);
        return;
    end

    fd_search_range_factor = params_proc.phase_est.fd_search_range_factor;
    num_fd_pts = params_proc.phase_est.ka_search_pts; 
    ka_search_pts_num = params_proc.phase_est.ka_search_pts;
    kb_search_pts_num = params_proc.phase_est.kb_search_pts;
    
    % ISAR中，相位估计的物理意义：
    % fd: 多普勒中心频率，对应于散射点相对雷达的平均径向速度
    % ka: 多普勒调频率，对应于散射点相对雷达的径向加速度
    % kb: 多普勒调频的变化率，对应于散射点的加加速度（急动度）
    
    temp_coeffs = poly_coeffs_prev_k; % Start with previous iteration's coefficients
    min_total_cost = inf; % Initialize with a very high cost

    for pass = 1:num_refinement_passes
        % 搜索fd (多普勒频率) - 对应ISAR中的平均旋转速度分量
        if poly_order >= 1
            fd_center = temp_coeffs(1);
            fd_search_half_range = fd_search_range_factor * 0.5 / (2^ (pass-1) ); % Shrinking search range
            fd_search_values = linspace(fd_center - fd_search_half_range, fd_center + fd_search_half_range, num_fd_pts);
            fd_search_values = utilidad_wrap_phase_coeffs(fd_search_values, 0.5);
            
            % 跳过搜索空间为空的情况
            if isempty(fd_search_values)
                fd_search_values = fd_center;
            end
            
            current_best_fd_cost = inf;
            best_fd_val_pass = temp_coeffs(1);

            for fd_val = fd_search_values
                temp_coeffs(1) = fd_val;
                current_phase = construct_phase_poly(tm_normalized, temp_coeffs);
                compensated_signal_fft = fft(signal_mode_k .* exp(-1j * current_phase) .* azimuth_window); % Apply window
                
                % 在ISAR成像中，好的相位补偿应该产生集中的频谱，对应清晰的散射点
                % 使用两个评价标准：频谱集中度和与目标匹配度
                
                % 1. 频谱集中度评价 - 使用L4/L2归一化，高值表示更集中的频谱
                sharpness_val = sum(abs(compensated_signal_fft).^4) / (sum(abs(compensated_signal_fft).^2)^2 + eps); 
                
                % 2. 目标匹配度评价 - 与目标频谱的差异
                match_error_val = 0.5 * norm(Residual_Target_Spectrum_k - compensated_signal_fft)^2;
                
                % 总成本：平衡频谱集中度和匹配度
                % 物理上，这相当于平衡散射点的分辨率和信号的保真度
                cost = -sharpness_weight * sharpness_val + match_error_val;

                if cost < current_best_fd_cost
                    current_best_fd_cost = cost;
                    best_fd_val_pass = fd_val;
                end
            end
            temp_coeffs(1) = best_fd_val_pass;
            min_total_cost = current_best_fd_cost; % Update overall min_cost
        end
       
        % 搜索ka (多普勒调频率) - 对应ISAR中的径向加速度
        if poly_order >= 2
            ka_center = temp_coeffs(2);
            max_chirp_rate_heuristic = (PRF/2)^2 * 0.1 / (2^ (pass-1) ); % Shrinking search range
            ka_norm_max_abs_heuristic = max_chirp_rate_heuristic / PRF^2 * 0.5;
            ka_search_values = linspace(ka_center - ka_norm_max_abs_heuristic, ka_center + ka_norm_max_abs_heuristic, ka_search_pts_num);
            
            current_best_ka_cost = min_total_cost; % Continue from previous best cost
            best_ka_val_pass = temp_coeffs(2);

            for ka_val = ka_search_values
                temp_coeffs(2) = ka_val;
                current_phase = construct_phase_poly(tm_normalized, temp_coeffs);
                compensated_signal_fft = fft(signal_mode_k .* exp(-1j * current_phase) .* azimuth_window);
                
                % 在ISAR中，好的加速度补偿应该进一步提高频谱集中度
                sharpness_val = sum(abs(compensated_signal_fft).^4) / (sum(abs(compensated_signal_fft).^2)^2 + eps);
                match_error_val = 0.5 * norm(Residual_Target_Spectrum_k - compensated_signal_fft)^2;
                
                % 使用与fd相同的评价标准
                cost = -sharpness_weight * sharpness_val + match_error_val;

                if cost < current_best_ka_cost
                    current_best_ka_cost = cost;
                    best_ka_val_pass = ka_val;
                end
            end
            temp_coeffs(2) = best_ka_val_pass;
            min_total_cost = current_best_ka_cost;
        end

        % 搜索kb (多普勒调频变化率) - 对应ISAR中的径向急动度
        if poly_order >= 3
            kb_center = temp_coeffs(3);
            kb_norm_max_abs_heuristic = (PRF/2)^3 / PRF^3 * 0.05 / (2^ (pass-1) ); % Shrinking
            kb_search_values = linspace(kb_center - kb_norm_max_abs_heuristic, kb_center + kb_norm_max_abs_heuristic, kb_search_pts_num);

            current_best_kb_cost = min_total_cost;
            best_kb_val_pass = temp_coeffs(3);

            for kb_val = kb_search_values
                temp_coeffs(3) = kb_val;
                current_phase = construct_phase_poly(tm_normalized, temp_coeffs);
                compensated_signal_fft = fft(signal_mode_k .* exp(-1j * current_phase) .* azimuth_window);
                
                % 在ISAR中，高阶相位补偿对快速机动目标很重要
                sharpness_val = sum(abs(compensated_signal_fft).^4) / (sum(abs(compensated_signal_fft).^2)^2 + eps);
                match_error_val = 0.5 * norm(Residual_Target_Spectrum_k - compensated_signal_fft)^2;
                
                % 使用与fd相同的评价标准
                cost = -sharpness_weight * sharpness_val + match_error_val;

                if cost < current_best_kb_cost
                    current_best_kb_cost = cost;
                    best_kb_val_pass = kb_val;
                end
            end
            temp_coeffs(3) = best_kb_val_pass;
            min_total_cost = current_best_kb_cost;
        end
    end % End refinement passes
    
    poly_coeffs_updated_k = temp_coeffs;
    estimated_phase_updated_k = construct_phase_poly(tm_normalized, poly_coeffs_updated_k);
end
function wrapped_coeffs = utilidad_wrap_phase_coeffs(coeffs, max_abs_val)
    wrapped_coeffs = mod(coeffs + max_abs_val, 2*max_abs_val) - max_abs_val;
end
function [u_k_updated, omega_k_updated] = update_modes_admm(target_signal_for_vmd, u_k_prev, omega_k_prev, phase_models_k, params_proc, rho_U)
    alpha_vmd = params_proc.vmd.alpha_vmd; 
    K = params_proc.vmd.K;
    tol_vmd_inner = params_proc.vmd.tol_vmd_inner;
    max_iter_vmd_inner = params_proc.vmd.max_iter_vmd_inner;
    alpha_phase_guidance = params_proc.vmd.alpha_phase_guidance; 
    
    N = length(target_signal_for_vmd);
    target_signal_fft = fft(target_signal_for_vmd); % target_signal_for_vmd is already zero-mean
    f_axis_normalized = params_proc.normalized_tm; 

    u_k = u_k_prev;
    omega_k = omega_k_prev;
    u_k_fft = zeros(K, N, 'like', 1j*target_signal_fft(1));
    for k_idx = 1:K, u_k_fft(k_idx,:) = fft(u_k(k_idx,:)); end
    
    u_sum_fft_prev_iter_start_loop = sum(u_k_fft,1); 

    for iter_inner = 1:max_iter_vmd_inner
        for k_idx = 1:K
            sum_other_modes_fft = sum(u_k_fft,1) - u_k_fft(k_idx,:); 
            numerator_fft = target_signal_fft - sum_other_modes_fft;
            denominator = 1 + 2*alpha_vmd*(f_axis_normalized - omega_k(k_idx)).^2;
            
            if alpha_phase_guidance > 0 && ~isempty(phase_models_k) && size(phase_models_k,1) >= k_idx && any(phase_models_k(k_idx,:)) && ~all(isnan(phase_models_k(k_idx,:)))
                phase_prior_signal_time = exp(1j * phase_models_k(k_idx,:)); 
                phase_prior_term_fft = fft(phase_prior_signal_time);
                numerator_fft = numerator_fft + alpha_phase_guidance * alpha_vmd * phase_prior_term_fft; 
                denominator = denominator + alpha_phase_guidance * alpha_vmd;
            end
            
            u_k_fft(k_idx,:) = numerator_fft ./ denominator;
            power_spectrum_uk = abs(u_k_fft(k_idx,:)).^2;
            if sum(power_spectrum_uk) > 1e-12
                omega_k(k_idx) = sum(f_axis_normalized .* power_spectrum_uk) / sum(power_spectrum_uk);
                omega_k(k_idx) = mod(omega_k(k_idx), 1); 
            end
        end 
        u_k = ifft(u_k_fft, [], 2); 
        if iter_inner > 1
            current_sum_fft = sum(u_k_fft,1);
            change_sum_fft = norm(current_sum_fft - u_sum_fft_prev_iter_start_loop) / (norm(u_sum_fft_prev_iter_start_loop) + eps);
            if change_sum_fft < tol_vmd_inner, break; end
            u_sum_fft_prev_iter_start_loop = current_sum_fft; 
        else
             u_sum_fft_prev_iter_start_loop = sum(u_k_fft,1); 
        end
    end 
    u_k_updated = u_k; 
    omega_k_updated = omega_k;
end
function y = soft_threshold(x, threshold_val)
    y = sign(x) .* max(abs(x) - threshold_val, 0);
end
function phase_poly = construct_phase_poly(tm_normalized, coeffs)
    poly_order = length(coeffs);
    phase_poly = zeros(size(tm_normalized));
    if poly_order >= 1 && ~isnan(coeffs(1)), phase_poly = phase_poly + 2*pi * coeffs(1) * tm_normalized; end
    if poly_order >= 2 && ~isnan(coeffs(2)), phase_poly = phase_poly + 2*pi * 0.5 * coeffs(2) * tm_normalized.^2; end
    if poly_order >= 3 && ~isnan(coeffs(3)), phase_poly = phase_poly + 2*pi * (1/6) * coeffs(3) * tm_normalized.^3; end
end

