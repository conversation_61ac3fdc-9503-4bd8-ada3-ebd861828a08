% run_isar_processing_enhanced.m
% 主脚本，用于运行ISAR回波仿真和增强的深度融合VMD-ADMM-DCFT成像处理
clear;clc;
fprintf('开始ISAR成像仿真与增强型深度融合处理...\n');

% -------------------- 1. 数据加载与仿真参数 -------------------- %
fprintf('加载/生成雷达回波数据...\n');
tic;
try
    load shipx2.mat; 
    load s_r_tm2.mat
    echo_data = s_r_tm2;
    fprintf('实际数据 shipx2_1000.mat 加载成功。\n');
    sim_params = struct();
    sim_params.Num_r = size(echo_data, 1);
    sim_params.Num_tm = size(echo_data, 2);
    sim_params.PRF = 1400; 
    sim_params.fc = 5.2e9;  
    sim_params.c = 3e8;
    sim_params.B = 80e6;    
    delta_r_res_actual = sim_params.c / (2*sim_params.B);
    r_center_actual = 0; 
    sim_params.r_axis = linspace(r_center_actual - (sim_params.Num_r/2)*delta_r_res_actual, ...
                                 r_center_actual + (sim_params.Num_r/2-1)*delta_r_res_actual, sim_params.Num_r);
    sim_params.tm = linspace(0, (sim_params.Num_tm-1)/sim_params.PRF, sim_params.Num_tm);
catch
    fprintf('未找到 shipx2_1000.mat，生成仿真数据...\n');
    [echo_data, sim_params] = generate_simulated_echo(); 
end

fprintf('对原始回波数据进行预处理 (去均值)...\n');
for r_bin = 1:size(echo_data, 1)
    echo_data(r_bin, :) = echo_data(r_bin, :) - mean(echo_data(r_bin, :));
end

fprintf('数据加载/生成完毕。耗时: %.2f 秒\n', toc);
fprintf('回波数据尺寸: %d (距离单元) x %d (方位单元)\n', size(echo_data, 1), size(echo_data, 2));

% -------------------- 2. 设置处理参数 -------------------- %
params_proc = struct();
% === VMD 参数 (重点调试区域) ===
params_proc.vmd.K = 3; % 尝试减小K值，例如 2 或 1 (K=1时VMD不起作用，直接处理原信号)
params_proc.vmd.alpha_vmd = 2000; % VMD带宽约束，可尝试 500, 1000, 4000等
params_proc.vmd.tau_vmd = 0;            
params_proc.vmd.tol_vmd_inner = 1e-6; % 提高VMD内部收敛精度
params_proc.vmd.max_iter_vmd_inner = 50; % 增加VMD内部迭代
params_proc.vmd.init_omega_method = 'peaks_robust'; % 'peaks', 'linear', 'peaks_robust'
params_proc.vmd.alpha_phase_guidance =0.005; % 降低相位引导权重，观察效果

% === 相位估计参数 (重点调试区域) ===
params_proc.phase_est.poly_order = 2; % 尝试降低阶数，例如 2 或 1
params_proc.phase_est.fd_search_range_factor = 0.5; 
params_proc.phase_est.ka_search_pts = 31;    
params_proc.phase_est.kb_search_pts = 31;    
params_proc.phase_est.sharpness_weight = 0.05; % 调整此权重，尝试 0, 0.001, 0.1 等
params_proc.phase_est.num_refinement_passes = 1; % 减少迭代优化次数以加速调试，稳定后再增加

% === ADMM 参数 ===
params_proc.admm_global.rho_X = 1.0;      
params_proc.admm_global.rho_U = 0.5;      
params_proc.admm_global.lambda_sparsity = 0.02; % 进一步减小稀疏权重
params_proc.admm_global.max_iter = 3;   % 保持足够的ADMM迭代
params_proc.admm_global.tol = 1e-4;     

params_proc.apply_azimuth_window = true; 
params_proc.visualize_vmd_spectra = true; % 新增：控制是否可视化VMD模态频谱
params_proc.r_bin_for_vmd_vis = -1; % -1 表示自动选择能量最大的，否则指定距离单元索引

params_proc.num_azimuth = sim_params.Num_tm; 
params_proc.num_range_bins = sim_params.Num_r; 
params_proc.PRF = sim_params.PRF;
params_proc.fc = sim_params.fc;
params_proc.c = sim_params.c;
params_proc.tm_azimuth = sim_params.tm; 
params_proc.normalized_tm = (0:params_proc.num_azimuth-1) / params_proc.num_azimuth; 

% -------------------- 3. 执行增强型深度融合ISAR成像算法 -------------------- %
fprintf('开始执行增强型深度融合VMD-ADMM-DCFT ISAR成像算法...\n');
tic;
[ISAR_image_fused, dominant_mode_compensated_fft, vmd_modes_all_bins, phase_coeffs_all_bins, admm_convergence_all_bins] = ...
    perform_isar_imaging_fused_admm_enhanced(echo_data, params_proc, sim_params);
fprintf('增强型深度融合ISAR成像处理完毕。耗时: %.2f 秒\n', toc);

% -------------------- 4. 显示结果 -------------------- %
fprintf('显示成像结果...\n');
doppler_axis = linspace(-params_proc.PRF/2, params_proc.PRF/2, params_proc.num_azimuth);

figure('Name', '原始数据和直接FFT');
echo_data_for_fft_display = echo_data; 
raw_fft = fftshift(fft(echo_data_for_fft_display, [], 2), 2);
imagesc(doppler_axis, sim_params.r_axis, abs(raw_fft));
xlabel('多普勒频率 (Hz)'); ylabel('距离 (米)'); title('原始数据直接FFT (预处理, 加窗)'); colorbar; axis xy;

ISAR_image_fused_shifted = fftshift(ISAR_image_fused, 2);
figure('Name', '增强型深度融合ADMM成像结果');
imagesc(doppler_axis, sim_params.r_axis, abs(ISAR_image_fused_shifted));
xlabel('多普勒频率 (Hz)'); ylabel('距离 (米)'); title('增强型VMD-ADMM-DCFT ISAR结果'); colorbar; axis xy;

figure('Name', '对数尺度对比 - 直接FFT');
G_raw = 20*log10(abs(raw_fft)./max(abs(raw_fft(:)) + eps)); 
imagesc(doppler_axis, sim_params.r_axis, G_raw); caxis([-30,0]); % 调整动态范围
xlabel('多普勒频率 (Hz)'); ylabel('距离 (米)'); title('直接FFT (dB, 预处理, 加窗)'); colorbar; axis xy; colormap('jet');

figure('Name', '对数尺度对比 - 增强型融合');
G_fused = 20*log10(abs(ISAR_image_fused_shifted)./max(abs(ISAR_image_fused_shifted(:)) + eps)); 
imagesc(doppler_axis, sim_params.r_axis, G_fused); caxis([-40,0]); % 调整动态范围
xlabel('多普勒频率 (Hz)'); ylabel('距离 (米)'); title('增强型融合ADMM (dB)'); colorbar; axis xy; colormap('jet');

if exist('dominant_mode_compensated_fft', 'var') && ~isempty(dominant_mode_compensated_fft)
    ISAR_image_fft_dominant_shifted = fftshift(dominant_mode_compensated_fft, 2);
    figure('Name', '对数尺度对比 - 主导模态补偿');
    G_dominant = 20*log10(abs(ISAR_image_fft_dominant_shifted)./max(abs(ISAR_image_fft_dominant_shifted(:)) + eps)); 
    imagesc(doppler_axis, sim_params.r_axis, G_dominant); caxis([-40,0]);
    xlabel('多普勒频率 (Hz)'); ylabel('距离 (米)'); title('主导模态补偿+FFT (dB)'); colorbar; axis xy; colormap('jet');
end

% -------------------- 辅助函数区 -------------------- %
function [ISAR_image_sparse, s_compensated_dominant_mode_fft, vmd_modes_all_bins, phase_coeffs_all_bins, admm_convergence_all_bins] = ...
    perform_isar_imaging_fused_admm_enhanced(radar_data, params_proc, sim_params)

    [num_range_bins, num_azimuth] = size(radar_data);
    fprintf('  处理数据尺寸: %d x %d\n', num_range_bins, num_azimuth);

    K_vmd = params_proc.vmd.K;
    poly_order_phase = params_proc.phase_est.poly_order;
    tm_normalized = params_proc.normalized_tm; 

    rho_X = params_proc.admm_global.rho_X;
    rho_U = params_proc.admm_global.rho_U;
    lambda_sparsity = params_proc.admm_global.lambda_sparsity;
    max_admm_iter = params_proc.admm_global.max_iter;
    admm_tol = params_proc.admm_global.tol;
    
    sharpness_weight = params_proc.phase_est.sharpness_weight; 
    num_phase_refinement_passes = params_proc.phase_est.num_refinement_passes;
    apply_azimuth_window_flag = params_proc.apply_azimuth_window;
    visualize_vmd_spectra_flag = params_proc.visualize_vmd_spectra;
    r_bin_for_vmd_vis_param = params_proc.r_bin_for_vmd_vis;


    ISAR_image_sparse = zeros(num_range_bins, num_azimuth, 'like', 1j*radar_data(1));
    s_compensated_dominant_mode_fft = zeros(num_range_bins, num_azimuth, 'like', 1j*radar_data(1));
    
    vmd_modes_all_bins = cell(num_range_bins, 1); % Store final VMD modes for each range bin
    phase_coeffs_all_bins = cell(num_range_bins, 1);
    admm_convergence_all_bins = cell(num_range_bins, 1);

    azimuth_window = ones(1, num_azimuth);
    if apply_azimuth_window_flag
        azimuth_window = hamming(num_azimuth)'; 
    end
    
    doppler_axis_vis = linspace(-params_proc.PRF/2, params_proc.PRF/2, num_azimuth); % For VMD spectra vis

    % Determine the single range bin for VMD spectra visualization throughout the run
    r_idx_vmd_spectra_vis = -1;
    if visualize_vmd_spectra_flag
        if r_bin_for_vmd_vis_param == -1
            [~, r_idx_vis_candidates] = sort(sum(abs(radar_data).^2, 2), 'descend');
            if ~isempty(r_idx_vis_candidates)
                r_idx_vmd_spectra_vis = r_idx_vis_candidates(1);
            else
                r_idx_vmd_spectra_vis = round(num_range_bins/2);
            end
            if isempty(r_idx_vmd_spectra_vis) || r_idx_vmd_spectra_vis == 0 || r_idx_vmd_spectra_vis > num_range_bins
                r_idx_vmd_spectra_vis = 1;
            end
             fprintf('  将为距离单元 %d 可视化VMD模态频谱（每次ADMM迭代后）。\n', r_idx_vmd_spectra_vis);
        else
            r_idx_vmd_spectra_vis = r_bin_for_vmd_vis_param;
            fprintf('  将为指定的距离单元 %d 可视化VMD模态频谱（每次ADMM迭代后）。\n', r_idx_vmd_spectra_vis);
        end
    end


    fprintf('  开始逐距离单元处理 (共 %d 个)...\n', num_range_bins);
    
    for r_idx = 1:num_range_bins
        signal_orig_for_range_bin = radar_data(r_idx, :); 
        
        if sum(abs(signal_orig_for_range_bin).^2) < 1e-12 * num_azimuth 
            ISAR_image_sparse(r_idx, :) = fft(signal_orig_for_range_bin .* azimuth_window); 
            s_compensated_dominant_mode_fft(r_idx, :) = fft(signal_orig_for_range_bin .* azimuth_window);
            admm_iter_data_empty = struct('primal_res_X', [], 'dual_res_X', [], 'primal_res_U', [], 'dual_res_U', []);
            admm_convergence_all_bins{r_idx} = admm_iter_data_empty;
            vmd_modes_all_bins{r_idx} = zeros(K_vmd, num_azimuth, 'like', 1j*signal_orig_for_range_bin(1));
            phase_coeffs_all_bins{r_idx} = zeros(K_vmd, poly_order_phase);
            continue;
        end
        
        signal_norm_factor = max(abs(signal_orig_for_range_bin));
        if signal_norm_factor < eps, signal_norm_factor = 1; end 
        signal = signal_orig_for_range_bin / signal_norm_factor;
        % 'signal' is already pre-processed (mean removed) before this function call.
        % Re-ensure zero mean for VMD input if necessary, though radar_data should be fine.
        signal = signal - mean(signal); 


        u_k = zeros(K_vmd, num_azimuth, 'like', 1j*signal(1));
        omega_k = zeros(K_vmd, 1);
        
        fft_signal_abs = abs(fft(signal)); % FFT of zero-mean signal
        valid_omega_idx = 0; % Renamed from omega_k_idx to avoid conflict
        if strcmp(params_proc.vmd.init_omega_method, 'peaks_robust')
            [pks, locs] = findpeaks(fft_signal_abs, 'SortStr', 'descend');
            temp_omega_init_vals = zeros(K_vmd,1); % temp array for omegas
            min_freq_sep = 0.05; 
            last_added_omega_val = -inf; % Renamed
            for i_pk = 1:length(locs)
                current_omega_val = (locs(i_pk)-1)/num_azimuth; % Renamed
                % Avoid DC or Nyquist if K > 1, unless it's the only strong peak
                is_dc_or_nyquist = abs(current_omega_val) < 0.01 || abs(current_omega_val - 0.5) < 0.01 || abs(current_omega_val - 1.0) < 0.01;
                if K_vmd == 1 || ~is_dc_or_nyquist || (K_vmd > 1 && length(pks) == 1)
                   if abs(current_omega_val - last_added_omega_val) > min_freq_sep || valid_omega_idx == 0
                        valid_omega_idx = valid_omega_idx + 1;
                        temp_omega_init_vals(valid_omega_idx) = current_omega_val;
                        last_added_omega_val = current_omega_val;
                        if valid_omega_idx == K_vmd, break; end
                   end
                end
            end
            if valid_omega_idx > 0
                 omega_k(1:valid_omega_idx) = temp_omega_init_vals(1:valid_omega_idx);
            end
        elseif strcmp(params_proc.vmd.init_omega_method, 'peaks')
            [~, locs] = findpeaks(fft_signal_abs, 'SortStr', 'descend', 'NPeaks', K_vmd);
            if ~isempty(locs)
                valid_locs_count = min(length(locs), K_vmd); % Renamed
                omega_k(1:valid_locs_count) = (locs(1:valid_locs_count)-1)/num_azimuth;
                valid_omega_idx = valid_locs_count;
            end
        else 
             for k_idx_init_loop = 1:K_vmd, omega_k(k_idx_init_loop) = (k_idx_init_loop-1)/(K_vmd); end % Renamed
             valid_omega_idx = K_vmd;
        end
        if valid_omega_idx < K_vmd 
            unset_indices = (valid_omega_idx+1):K_vmd;
            if ~isempty(unset_indices)
                 omega_k(unset_indices) = linspace(0.1, 0.4, length(unset_indices))'; % Ensure column vector
            end
        end
        
        poly_coeffs_k = zeros(K_vmd, poly_order_phase); 
        estimated_phases_k = zeros(K_vmd, num_azimuth, 'like', 1j*signal(1)); 
        if poly_order_phase >= 1
            for k_init_phase = 1:K_vmd
                fd_norm_init = omega_k(k_init_phase);
                if fd_norm_init > 0.5, fd_norm_init = fd_norm_init - 1; end
                poly_coeffs_k(k_init_phase, 1) = fd_norm_init;
                estimated_phases_k(k_init_phase, :) = construct_phase_poly(tm_normalized, poly_coeffs_k(k_init_phase,:));
            end
        end

        X_sparse_spectrum = fft(signal .* azimuth_window); 
        Z_aux_X = X_sparse_spectrum; 
        Y_lagrange_X = zeros(size(X_sparse_spectrum), 'like', 1j*signal(1));
        Y_lagrange_U = zeros(size(signal), 'like', 1j*signal(1));
                                                               
        admm_iter_data = struct('primal_res_X', zeros(1,max_admm_iter), 'dual_res_X', zeros(1,max_admm_iter), ...
                                'primal_res_U', zeros(1,max_admm_iter), 'dual_res_U', zeros(1,max_admm_iter));
        u_k_prev_for_dual_U = u_k; 

        for iter_admm = 1:max_admm_iter
            X_prev_for_dual_X = X_sparse_spectrum; 
            Z_aux_X_prev_for_dual_X = Z_aux_X; 
            
            target_signal_for_vmd = signal + Y_lagrange_U / rho_U; 
            target_signal_for_vmd = target_signal_for_vmd - mean(target_signal_for_vmd); 

            current_phase_models_for_vmd = estimated_phases_k; 
            [u_k_current_iter, omega_k_current_iter] = update_modes_admm(target_signal_for_vmd, u_k, omega_k, current_phase_models_for_vmd, params_proc, rho_U);
            u_k = u_k_current_iter; % Update u_k
            omega_k = omega_k_current_iter; % Update omega_k

            % --- VMD模态频谱可视化 (调试用) ---
            if visualize_vmd_spectra_flag && r_idx == r_idx_vmd_spectra_vis && mod(iter_admm, 5) == 0 % Visualize every 5 ADMM iterations
                figure_name_vmd_spectra = sprintf('VMD模态频谱 (距离单元 %d, ADMM迭代 %d)', r_idx, iter_admm);
                if ishandle(findobj('type','figure','name',figure_name_vmd_spectra))
                    clf(findobj('type','figure','name',figure_name_vmd_spectra)); % Clear previous plot
                end
                figure('Name', figure_name_vmd_spectra, 'NumberTitle', 'off');
                sgtitle(sprintf('距离单元 %d, ADMM迭代 %d', r_idx, iter_admm));
                for k_vis = 1:K_vmd
                    subplot(K_vmd, 1, k_vis);
                    uk_vis_fft = abs(fftshift(fft(u_k(k_vis,:) - mean(u_k(k_vis,:)))));
                    plot(doppler_axis_vis, uk_vis_fft);
                    title(sprintf('模态 %d (omega=%.2f), 频谱', k_vis, omega_k(k_vis)));
                    ylabel('幅度');
                    if k_vis == K_vmd, xlabel('多普勒频率 (Hz)'); end
                end
                drawnow; % Force update of the plot
            end
            % --- 可视化结束 ---
            
            S_reconstructed_from_modes_new = sum(u_k, 1);
            X_target_global = X_sparse_spectrum + Y_lagrange_X / rho_X;
            
            temp_sum_compensated_others_fft = zeros(1, num_azimuth, 'like', 1j*signal(1));
            for k_other_init = 1:K_vmd 
                uk_other_processed = u_k(k_other_init,:) - mean(u_k(k_other_init,:));
                temp_sum_compensated_others_fft = temp_sum_compensated_others_fft + ...
                    fft( uk_other_processed .* exp(-1j * estimated_phases_k(k_other_init,:)) .* azimuth_window );
            end

            new_poly_coeffs_k = poly_coeffs_k; 
            new_estimated_phases_k = estimated_phases_k;

            for k_update_phase = 1:K_vmd
                uk_current_mode_processed = u_k(k_update_phase,:) - mean(u_k(k_update_phase,:));
                current_uk_compensated_fft_old_phase = fft(uk_current_mode_processed .* exp(-1j * estimated_phases_k(k_update_phase,:)) .* azimuth_window);
                Residual_Target_Spectrum_k = X_target_global - (temp_sum_compensated_others_fft - current_uk_compensated_fft_old_phase);

                [coeffs_k, phase_val_k] = update_phase_coeffs_admm_enhanced(...
                    uk_current_mode_processed, ...
                    poly_coeffs_k(k_update_phase,:), ...
                    Residual_Target_Spectrum_k, ...
                    params_proc, tm_normalized, sharpness_weight, azimuth_window);
                
                new_poly_coeffs_k(k_update_phase,:) = coeffs_k;
                new_estimated_phases_k(k_update_phase,:) = phase_val_k;
                
                temp_sum_compensated_others_fft = temp_sum_compensated_others_fft - current_uk_compensated_fft_old_phase ...
                                                + fft(uk_current_mode_processed .* exp(-1j*phase_val_k) .* azimuth_window);
            end
            poly_coeffs_k = new_poly_coeffs_k;
            estimated_phases_k = new_estimated_phases_k;
            
            s_compensated_time = zeros(1, num_azimuth, 'like', signal(1));
            for k_idx = 1:K_vmd
                s_compensated_time = s_compensated_time + (u_k(k_idx,:) - mean(u_k(k_idx,:))) .* exp(-1j * estimated_phases_k(k_idx,:));
            end
            s_compensated_time = s_compensated_time - mean(s_compensated_time); 
            S_compensated_fft = fft(s_compensated_time .* azimuth_window); 
            
            X_sparse_spectrum = (S_compensated_fft + rho_X * (Z_aux_X - Y_lagrange_X/rho_X)) / (1 + rho_X);
            Z_aux_X = soft_threshold(X_sparse_spectrum + Y_lagrange_X/rho_X, lambda_sparsity/rho_X);
            
            Y_lagrange_X = Y_lagrange_X + rho_X * (X_sparse_spectrum - Z_aux_X);
            Y_lagrange_U = Y_lagrange_U + rho_U * (signal - S_reconstructed_from_modes_new); 
            
            norm_X_prev = norm(X_prev_for_dual_X); if norm_X_prev < eps, norm_X_prev = 1; end
            norm_Y_X_curr = norm(Y_lagrange_X); if norm_Y_X_curr < eps, norm_Y_X_curr = 1; end
            norm_signal_curr = norm(signal); if norm_signal_curr < eps, norm_signal_curr = 1; end
            norm_Y_U_curr = norm(Y_lagrange_U); if norm_Y_U_curr < eps, norm_Y_U_curr = 1; end

            primal_res_X_val = norm(X_sparse_spectrum - Z_aux_X) / norm_X_prev;
            dual_res_X_val = rho_X * norm(Z_aux_X - Z_aux_X_prev_for_dual_X) / norm_Y_X_curr;
            primal_res_U_val = norm(signal - S_reconstructed_from_modes_new) / norm_signal_curr;
            dual_res_U_val = rho_U * norm(sum(u_k,1) - sum(u_k_prev_for_dual_U,1)) / norm_Y_U_curr; 
            
            admm_iter_data.primal_res_X(iter_admm) = primal_res_X_val;
            admm_iter_data.dual_res_X(iter_admm) = dual_res_X_val;
            admm_iter_data.primal_res_U(iter_admm) = primal_res_U_val;
            admm_iter_data.dual_res_U(iter_admm) = dual_res_U_val;
            u_k_prev_for_dual_U = u_k; 

            if iter_admm > 1 && ...
               ((primal_res_X_val < admm_tol && dual_res_X_val < admm_tol) && ...
                (primal_res_U_val < admm_tol && dual_res_U_val < admm_tol))
                admm_iter_data.primal_res_X = admm_iter_data.primal_res_X(1:iter_admm);
                admm_iter_data.dual_res_X = admm_iter_data.dual_res_X(1:iter_admm);
                admm_iter_data.primal_res_U = admm_iter_data.primal_res_U(1:iter_admm);
                admm_iter_data.dual_res_U = admm_iter_data.dual_res_U(1:iter_admm);
                break;
            end
        end 
        
        ISAR_image_sparse(r_idx, :) = X_sparse_spectrum * signal_norm_factor; 
        vmd_modes_all_bins{r_idx} = u_k * signal_norm_factor; % Store the final u_k for this range bin
        phase_coeffs_all_bins{r_idx} = poly_coeffs_k; 
        admm_convergence_all_bins{r_idx} = admm_iter_data;
        
        mode_energies = sum(abs(u_k).^2, 2);
        [~, dominant_idx_candidates] = sort(mode_energies, 'descend');
        if ~isempty(dominant_idx_candidates)
            dominant_idx = dominant_idx_candidates(1);
            dominant_phase_compensation = construct_phase_poly(tm_normalized, poly_coeffs_k(dominant_idx, :));
            s_comp_dom_mode_time = (signal_orig_for_range_bin - mean(signal_orig_for_range_bin)) .* exp(-1j * dominant_phase_compensation); 
            s_comp_dom_mode_time = s_comp_dom_mode_time - mean(s_comp_dom_mode_time);
            s_compensated_dominant_mode_fft(r_idx, :) = fft(s_comp_dom_mode_time .* azimuth_window);
        else
             s_comp_dom_mode_time_fallback = signal_orig_for_range_bin - mean(signal_orig_for_range_bin);
             s_compensated_dominant_mode_fft(r_idx, :) = fft(s_comp_dom_mode_time_fallback .* azimuth_window);
        end
    end 
    fprintf('  所有距离单元处理完毕。\n');
end

function [poly_coeffs_updated_k, estimated_phase_updated_k] = update_phase_coeffs_admm_enhanced(...
    signal_mode_k, poly_coeffs_prev_k, Residual_Target_Spectrum_k, ...
    params_proc, tm_normalized, sharpness_weight, azimuth_window)

    poly_order = params_proc.phase_est.poly_order;
    PRF = params_proc.PRF; 
    N = length(tm_normalized);
    num_refinement_passes = params_proc.phase_est.num_refinement_passes;

    if sum(abs(signal_mode_k)) < 1e-9 
        poly_coeffs_updated_k = zeros(1, poly_order);
        estimated_phase_updated_k = zeros(1, N);
        return;
    end

    fd_search_range_factor = params_proc.phase_est.fd_search_range_factor;
    num_fd_pts = params_proc.phase_est.ka_search_pts; 
    ka_search_pts_num = params_proc.phase_est.ka_search_pts;
    kb_search_pts_num = params_proc.phase_est.kb_search_pts;
    
    temp_coeffs = poly_coeffs_prev_k; 
    min_total_cost = inf; 

    for pass = 1:num_refinement_passes
        if poly_order >= 1
            fd_center = temp_coeffs(1);
            fd_search_half_range = fd_search_range_factor * 0.5 / (2^ (pass-1) ); 
            fd_search_values = linspace(fd_center - fd_search_half_range, fd_center + fd_search_half_range, num_fd_pts);
            fd_search_values = utilidad_wrap_phase_coeffs(fd_search_values, 0.5);
            current_best_fd_cost = min_total_cost; % Initialize with overall best from previous full pass or inf
            if pass > 1 && isinf(current_best_fd_cost) % Ensure it's not stuck at inf if previous pass improved
                 % Re-evaluate cost with current temp_coeffs if needed, or use a very large number
                 current_phase_eval = construct_phase_poly(tm_normalized, temp_coeffs);
                 compensated_fft_eval = fft(signal_mode_k .* exp(-1j * current_phase_eval) .* azimuth_window);
                 sharp_eval = sum(abs(compensated_fft_eval).^4) / (sum(abs(compensated_fft_eval).^2)^2 + eps);
                 match_eval = 0.5 * norm(Residual_Target_Spectrum_k - compensated_fft_eval)^2;
                 current_best_fd_cost = -sharpness_weight * sharp_eval + match_eval;
            elseif isinf(current_best_fd_cost) % First pass, first coefficient
                current_best_fd_cost = inf;
            end
            best_fd_val_pass = temp_coeffs(1);

            for fd_val = fd_search_values
                temp_coeffs(1) = fd_val;
                current_phase = construct_phase_poly(tm_normalized, temp_coeffs);
                compensated_signal_fft = fft(signal_mode_k .* exp(-1j * current_phase) .* azimuth_window); 
                sharpness_val = sum(abs(compensated_signal_fft).^4) / (sum(abs(compensated_signal_fft).^2)^2 + eps); 
                match_error_val = 0.5 * norm(Residual_Target_Spectrum_k - compensated_signal_fft)^2;
                cost = -sharpness_weight * sharpness_val + match_error_val;
                if cost < current_best_fd_cost
                    current_best_fd_cost = cost;
                    best_fd_val_pass = fd_val;
                end
            end
            temp_coeffs(1) = best_fd_val_pass;
            min_total_cost = current_best_fd_cost; 
        end

        if poly_order >= 2
            ka_center = temp_coeffs(2);
            max_chirp_rate_heuristic = (PRF/2)^2 * 0.1 / (2^ (pass-1) ); 
            ka_norm_max_abs_heuristic = max_chirp_rate_heuristic / PRF^2 * 0.5;
            ka_search_values = linspace(ka_center - ka_norm_max_abs_heuristic, ka_center + ka_norm_max_abs_heuristic, ka_search_pts_num);
            current_best_ka_cost = min_total_cost; 
            best_ka_val_pass = temp_coeffs(2);
            for ka_val = ka_search_values
                temp_coeffs(2) = ka_val;
                current_phase = construct_phase_poly(tm_normalized, temp_coeffs);
                compensated_signal_fft = fft(signal_mode_k .* exp(-1j * current_phase) .* azimuth_window);
                sharpness_val = sum(abs(compensated_signal_fft).^4) / (sum(abs(compensated_signal_fft).^2)^2 + eps);
                match_error_val = 0.5 * norm(Residual_Target_Spectrum_k - compensated_signal_fft)^2;
                cost = -sharpness_weight * sharpness_val + match_error_val;
                if cost < current_best_ka_cost
                    current_best_ka_cost = cost;
                    best_ka_val_pass = ka_val;
                end
            end
            temp_coeffs(2) = best_ka_val_pass;
            min_total_cost = current_best_ka_cost;
        end

        if poly_order >= 3
            kb_center = temp_coeffs(3);
            kb_norm_max_abs_heuristic = (PRF/2)^3 / PRF^3 * 0.05 / (2^ (pass-1) ); 
            kb_search_values = linspace(kb_center - kb_norm_max_abs_heuristic, kb_center + kb_norm_max_abs_heuristic, kb_search_pts_num);
            current_best_kb_cost = min_total_cost;
            best_kb_val_pass = temp_coeffs(3);
            for kb_val = kb_search_values
                temp_coeffs(3) = kb_val;
                current_phase = construct_phase_poly(tm_normalized, temp_coeffs);
                compensated_signal_fft = fft(signal_mode_k .* exp(-1j * current_phase) .* azimuth_window);
                sharpness_val = sum(abs(compensated_signal_fft).^4) / (sum(abs(compensated_signal_fft).^2)^2 + eps);
                match_error_val = 0.5 * norm(Residual_Target_Spectrum_k - compensated_signal_fft)^2;
                cost = -sharpness_weight * sharpness_val + match_error_val;
                if cost < current_best_kb_cost
                    current_best_kb_cost = cost;
                    best_kb_val_pass = kb_val;
                end
            end
            temp_coeffs(3) = best_kb_val_pass;
            min_total_cost = current_best_kb_cost;
        end
    end 
    
    poly_coeffs_updated_k = temp_coeffs;
    estimated_phase_updated_k = construct_phase_poly(tm_normalized, poly_coeffs_updated_k);
end

function wrapped_coeffs = utilidad_wrap_phase_coeffs(coeffs, max_abs_val)
    wrapped_coeffs = mod(coeffs + max_abs_val, 2*max_abs_val) - max_abs_val;
end

function [u_k_updated, omega_k_updated] = update_modes_admm(target_signal_for_vmd, u_k_prev, omega_k_prev, phase_models_k, params_proc, rho_U)
    alpha_vmd = params_proc.vmd.alpha_vmd; 
    K = params_proc.vmd.K;
    tol_vmd_inner = params_proc.vmd.tol_vmd_inner;
    max_iter_vmd_inner = params_proc.vmd.max_iter_vmd_inner;
    alpha_phase_guidance = params_proc.vmd.alpha_phase_guidance; 
    
    N = length(target_signal_for_vmd);
    target_signal_fft = fft(target_signal_for_vmd); 
    f_axis_normalized = params_proc.normalized_tm; 

    u_k = u_k_prev;
    omega_k = omega_k_prev;
    u_k_fft = zeros(K, N, 'like', 1j*target_signal_fft(1));
    for k_idx = 1:K, u_k_fft(k_idx,:) = fft(u_k(k_idx,:)); end
    
    u_sum_fft_prev_iter_start_loop = sum(u_k_fft,1); 

    for iter_inner = 1:max_iter_vmd_inner
        for k_idx = 1:K
            sum_other_modes_fft = sum(u_k_fft,1) - u_k_fft(k_idx,:); 
            numerator_fft = target_signal_fft - sum_other_modes_fft;
            denominator = 1 + 2*alpha_vmd*(f_axis_normalized - omega_k(k_idx)).^2;
            
            if alpha_phase_guidance > 0 && ~isempty(phase_models_k) && size(phase_models_k,1) >= k_idx && any(phase_models_k(k_idx,:)) && ~all(isnan(phase_models_k(k_idx,:)))
                phase_prior_signal_time = exp(1j * phase_models_k(k_idx,:)); 
                phase_prior_term_fft = fft(phase_prior_signal_time);
                numerator_fft = numerator_fft + alpha_phase_guidance * alpha_vmd * phase_prior_term_fft; 
                denominator = alpha_phase_guidance * alpha_vmd; %denominator +
            end
            
            u_k_fft(k_idx,:) = numerator_fft ./ denominator;
            power_spectrum_uk = abs(u_k_fft(k_idx,:)).^2;
            if sum(power_spectrum_uk) > 1e-12
                omega_k(k_idx) = sum(f_axis_normalized .* power_spectrum_uk) / sum(power_spectrum_uk);
                omega_k(k_idx) = mod(omega_k(k_idx), 1); 
            end
        end 
        u_k = ifft(u_k_fft, [], 2); 
        if iter_inner > 1
            current_sum_fft = sum(u_k_fft,1);
            change_sum_fft = norm(current_sum_fft - u_sum_fft_prev_iter_start_loop) / (norm(u_sum_fft_prev_iter_start_loop) + eps);
            if change_sum_fft < tol_vmd_inner, break; end
            u_sum_fft_prev_iter_start_loop = current_sum_fft; 
        else
             u_sum_fft_prev_iter_start_loop = sum(u_k_fft,1); 
        end
    end 
    u_k_updated = u_k; 
    omega_k_updated = omega_k;
end


function y = soft_threshold(x, threshold_val)
    y = sign(x) .* max(abs(x) - threshold_val, 0);
end

function phase_poly = construct_phase_poly(tm_normalized, coeffs)
    poly_order = length(coeffs);
    phase_poly = zeros(size(tm_normalized));
    if poly_order >= 1 && ~isnan(coeffs(1)), phase_poly = phase_poly + 2*pi * coeffs(1) * tm_normalized; end
    if poly_order >= 2 && ~isnan(coeffs(2)), phase_poly = phase_poly + 2*pi * 0.5 * coeffs(2) * tm_normalized.^2; end
    if poly_order >= 3 && ~isnan(coeffs(3)), phase_poly = phase_poly + 2*pi * (1/6) * coeffs(3) * tm_normalized.^3; end
end
